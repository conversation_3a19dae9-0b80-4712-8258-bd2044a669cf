# API Configuration
API_BASE_URL=https://your-domain.com/mobile-api
API_DOMAIN=your-domain.com

# API Endpoints
API_CHECK_STAFF_CREDENTIALS=/check-staff-credentials
API_CHECK_STUDENT_CREDENTIALS=/check-student-credentials
API_GET_STUDENT_TIMETABLE=/get-student-timetable2
API_GET_TEACHER_TIMETABLE=/get-teacher-timetable-data/
API_GET_STUDENT_GRADES=/get-student-grades
API_GET_STUDENT_ATTENDANCE=/get-student-attendance-data
API_GET_STUDENT_HOMEWORK=/get-student-homework-data
API_GET_STUDENT_BPS=/get-student-bps-data
API_GET_TEACHER_BPS=/get-teacher-bps-data/
API_GET_ATTENDANCE_DETAILS=/get-attendance-details/
API_STORE_BPS=/discipline/store-bps
API_DELETE_BPS=/discipline/delete-bps

# Web Resources
WEB_CALENDAR=/calendar
WEB_CONTACTS=/contacts
WEB_MESSAGES=/messages
WEB_ABOUT=/about
WEB_FAQ=/faq

# App Configuration
APP_NAME=EduSIS
APP_VERSION=1.0.0
APP_BUNDLE_ID=com.edunovaasia.edusis

# Development Configuration
USE_DUMMY_DATA=false
ENABLE_LOGGING=true

# Firebase Configuration (Note: These should be kept in separate config files for security)
# FIREBASE_PROJECT_ID=your-project-id
# FIREBASE_STORAGE_BUCKET=your-project-id.firebasestorage.app

# Network Configuration
NETWORK_TIMEOUT=10000
ENABLE_CLEARTEXT_TRAFFIC=true

# Device Configuration
DEFAULT_DEVICE_TYPE=ios
