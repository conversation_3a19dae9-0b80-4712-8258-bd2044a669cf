import {
  API_BASE_URL,
  API_DOMAIN,
  API_CHECK_STAFF_CREDENTIALS,
  API_CHECK_STUDENT_CREDENTIALS,
  API_GET_STUDENT_TIMETABLE,
  API_GET_TEACHER_TIMETABLE,
  API_GET_STUDENT_GRADES,
  API_GET_STUDENT_ATTENDANCE,
  API_GET_STUDENT_HOMEWORK,
  API_GET_STUDENT_BPS,
  API_GET_TEACHER_BPS,
  API_GET_ATTENDANCE_DETAILS,
  API_STORE_BPS,
  API_DELETE_BPS,
  WEB_CALENDAR,
  WEB_CONTACTS,
  WEB_MESSAGES,
  WEB_ABOUT,
  WEB_FAQ,
  APP_NAME,
  APP_VERSION,
  APP_BUNDLE_ID,
  USE_DUMMY_DATA,
  ENABLE_LOGGING,
  NETWORK_TIMEOUT,
  ENABLE_CLEARTEXT_TRAFFIC,
  DEFAULT_DEVICE_TYPE,
} from '@env';

// API Configuration
export const Config = {
  // Base API Configuration
  API_BASE_URL: API_BASE_URL || 'https://sis.bfi.edu.mm/mobile-api',
  API_DOMAIN: API_DOMAIN || 'sis.bfi.edu.mm',

  // API Endpoints
  API_ENDPOINTS: {
    CHECK_STAFF_CREDENTIALS:
      API_CHECK_STAFF_CREDENTIALS || '/check-staff-credentials',
    CHECK_STUDENT_CREDENTIALS:
      API_CHECK_STUDENT_CREDENTIALS || '/check-student-credentials',
    GET_STUDENT_TIMETABLE:
      API_GET_STUDENT_TIMETABLE || '/get-student-timetable2',
    GET_TEACHER_TIMETABLE:
      API_GET_TEACHER_TIMETABLE || '/get-teacher-timetable-data/',
    GET_STUDENT_GRADES: API_GET_STUDENT_GRADES || '/get-student-grades',
    GET_STUDENT_ATTENDANCE:
      API_GET_STUDENT_ATTENDANCE || '/get-student-attendance-data',
    GET_STUDENT_HOMEWORK:
      API_GET_STUDENT_HOMEWORK || '/get-student-homework-data',
    GET_STUDENT_BPS: API_GET_STUDENT_BPS || '/get-student-bps-data',
    GET_TEACHER_BPS: API_GET_TEACHER_BPS || '/get-teacher-bps-data/',
    GET_ATTENDANCE_DETAILS:
      API_GET_ATTENDANCE_DETAILS || '/get-attendance-details/',
    STORE_BPS: API_STORE_BPS || '/discipline/store-bps',
    DELETE_BPS: API_DELETE_BPS || '/discipline/delete-bps',
  },

  // Web Resources
  WEB_ENDPOINTS: {
    CALENDAR: WEB_CALENDAR || '/calendar',
    CONTACTS: WEB_CONTACTS || '/contacts',
    MESSAGES: WEB_MESSAGES || '/messages',
    ABOUT: WEB_ABOUT || '/about',
    FAQ: WEB_FAQ || '/faq',
  },

  // App Configuration
  APP: {
    NAME: APP_NAME || 'EduSIS',
    VERSION: APP_VERSION || '1.0.0',
    BUNDLE_ID: APP_BUNDLE_ID || 'com.edunovaasia.edusis',
  },

  // Development Configuration
  DEV: {
    USE_DUMMY_DATA: USE_DUMMY_DATA === 'true',
    ENABLE_LOGGING: ENABLE_LOGGING !== 'false',
  },

  // Network Configuration
  NETWORK: {
    TIMEOUT: parseInt(NETWORK_TIMEOUT) || 10000,
    ENABLE_CLEARTEXT_TRAFFIC: ENABLE_CLEARTEXT_TRAFFIC !== 'false',
  },

  // Device Configuration
  DEVICE: {
    DEFAULT_TYPE: DEFAULT_DEVICE_TYPE || 'ios',
  },
};

// Helper functions to build URLs
export const buildApiUrl = (endpoint, params = {}) => {
  const baseUrl = Config.API_BASE_URL;
  const url = new URL(endpoint, baseUrl);

  Object.keys(params).forEach((key) => {
    if (params[key] !== undefined && params[key] !== null) {
      url.searchParams.append(key, params[key]);
    }
  });

  return url.toString();
};

export const buildWebUrl = (endpoint, params = {}) => {
  const baseUrl = Config.API_BASE_URL;
  const url = new URL(endpoint, baseUrl);

  Object.keys(params).forEach((key) => {
    if (params[key] !== undefined && params[key] !== null) {
      url.searchParams.append(key, params[key]);
    }
  });

  return url.toString();
};

// Export individual configurations for backward compatibility
export const API_BASE_URL_CONFIG = Config.API_BASE_URL;
export const USE_DUMMY_DATA_CONFIG = Config.DEV.USE_DUMMY_DATA;

export default Config;
