import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { Config, buildApiUrl } from '../config/env';
import {
  faArrowLeft,
  faClipboardList,
  faCalculator,
  faFlask,
  faMicroscope,
  faAtom,
  faRunning,
  faLaptopCode,
  faGlobe,
  faPalette,
  faLandmark,
  faMapMarkedAlt,
  faLanguage,
  faMusic,
  faTheaterMasks,
  faCameraRetro,
  faTools,
  faBusinessTime,
  faBalanceScale,
  faHeartbeat,
  faLeaf,
  faBook,
  faChevronRight,
  faUser,
  faClock,
  faCalendarAlt,
  faCheckCircle,
  faExclamationTriangle,
} from '@fortawesome/free-solid-svg-icons';
import { useScreenOrientation } from '../hooks/useScreenOrientation';

export default function AssignmentsScreen({ navigation, route }) {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const { studentName, authCode } = route.params || {};
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [showCompleted, setShowCompleted] = useState(false);

  // Enable rotation for this screen
  useScreenOrientation(true);

  // Listen for orientation changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });

    return () => subscription?.remove();
  }, []);

  const isLandscape = screenData.width > screenData.height;

  // Helper function to get specific subject icon (same as GradesScreen)
  const getSubjectIcon = (subject) => {
    const subjectLower = subject.toLowerCase();

    // Mathematics
    if (
      subjectLower.includes('math') ||
      subjectLower.includes('algebra') ||
      subjectLower.includes('geometry') ||
      subjectLower.includes('calculus') ||
      subjectLower.includes('statistics')
    ) {
      return faCalculator;
    }

    // Sciences
    if (subjectLower.includes('physics')) return faAtom;
    if (subjectLower.includes('chemistry')) return faFlask;
    if (
      subjectLower.includes('biology') ||
      subjectLower.includes('life science')
    )
      return faMicroscope;
    if (subjectLower.includes('science') && !subjectLower.includes('computer'))
      return faFlask;

    // Languages
    if (
      subjectLower.includes('english') ||
      subjectLower.includes('language arts') ||
      subjectLower.includes('literature') ||
      subjectLower.includes('writing')
    ) {
      return faLanguage;
    }

    // Social Studies
    if (subjectLower.includes('history')) return faLandmark;
    if (subjectLower.includes('geography') || subjectLower.includes('geo'))
      return faMapMarkedAlt;
    if (
      subjectLower.includes('global perspective') ||
      subjectLower.includes('global studies') ||
      subjectLower.includes('world studies')
    )
      return faGlobe;

    // Technology & Computing
    if (
      subjectLower.includes('ict') ||
      subjectLower.includes('computer') ||
      subjectLower.includes('computing') ||
      subjectLower.includes('technology') ||
      subjectLower.includes('programming') ||
      subjectLower.includes('coding')
    ) {
      return faLaptopCode;
    }

    // Arts
    if (
      subjectLower.includes('art') ||
      subjectLower.includes('drawing') ||
      subjectLower.includes('painting') ||
      subjectLower.includes('design')
    ) {
      return faPalette;
    }
    if (
      subjectLower.includes('music') ||
      subjectLower.includes('band') ||
      subjectLower.includes('orchestra') ||
      subjectLower.includes('choir')
    ) {
      return faMusic;
    }
    if (
      subjectLower.includes('drama') ||
      subjectLower.includes('theater') ||
      subjectLower.includes('theatre') ||
      subjectLower.includes('acting')
    ) {
      return faTheaterMasks;
    }
    if (
      subjectLower.includes('photography') ||
      subjectLower.includes('media')
    ) {
      return faCameraRetro;
    }

    // Physical Education & Health
    if (
      subjectLower.includes('physical education') ||
      subjectLower.includes('pe') ||
      subjectLower.includes('sport') ||
      subjectLower.includes('fitness') ||
      subjectLower.includes('gym') ||
      subjectLower.includes('athletics')
    ) {
      return faRunning;
    }
    if (subjectLower.includes('health') || subjectLower.includes('wellness')) {
      return faHeartbeat;
    }

    // Business & Economics
    if (
      subjectLower.includes('business') ||
      subjectLower.includes('economics') ||
      subjectLower.includes('finance') ||
      subjectLower.includes('accounting')
    ) {
      return faBusinessTime;
    }

    // Law & Government
    if (
      subjectLower.includes('law') ||
      subjectLower.includes('government') ||
      subjectLower.includes('civics') ||
      subjectLower.includes('politics')
    ) {
      return faBalanceScale;
    }

    // Environmental Studies
    if (
      subjectLower.includes('environmental') ||
      subjectLower.includes('ecology') ||
      subjectLower.includes('earth science')
    ) {
      return faLeaf;
    }

    // Technical/Vocational
    if (
      subjectLower.includes('engineering') ||
      subjectLower.includes('technical') ||
      subjectLower.includes('workshop') ||
      subjectLower.includes('construction')
    ) {
      return faTools;
    }

    // Default fallback
    return faBook;
  };

  // Helper function to get random but consistent color for each subject (same as GradesScreen)
  const getSubjectColor = (subject) => {
    // Array of beautiful colors
    const colors = [
      '#FF9500', // Orange
      '#007AFF', // Blue
      '#34C759', // Green
      '#AF52DE', // Purple
      '#FF3B30', // Red
      '#5856D6', // Indigo
      '#FF2D92', // Pink
      '#FF9F0A', // Amber
      '#30D158', // Mint
      '#64D2FF', // Cyan
      '#BF5AF2', // Violet
      '#FF6482', // Rose
      '#32ADE6', // Light Blue
      '#FFD60A', // Yellow
      '#AC8E68', // Brown
    ];

    // Generate a consistent hash from the subject name
    let hash = 0;
    for (let i = 0; i < subject.length; i++) {
      const char = subject.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Use the hash to pick a color consistently
    const colorIndex = Math.abs(hash) % colors.length;
    return colors[colorIndex];
  };

  useEffect(() => {
    if (authCode) {
      fetchAssignmentsData();
    }
  }, [authCode]);

  // Helper function to group assignments by subject
  const getSubjectGroups = () => {
    console.log('getSubjectGroups called with assignments:', assignments);

    // Handle different possible API response structures
    let assignmentsArray = [];

    if (Array.isArray(assignments)) {
      assignmentsArray = assignments;
    } else if (
      assignments &&
      assignments.data &&
      Array.isArray(assignments.data)
    ) {
      assignmentsArray = assignments.data;
    } else if (assignments && typeof assignments === 'object') {
      // If assignments is an object, try to find an array property
      const possibleArrays = Object.values(assignments).filter((val) =>
        Array.isArray(val)
      );
      if (possibleArrays.length > 0) {
        assignmentsArray = possibleArrays[0];
      }
    }

    console.log('Processed assignments array:', assignmentsArray);

    if (!assignmentsArray || assignmentsArray.length === 0) {
      console.log('No assignments found in array');
      return [];
    }

    const groups = assignmentsArray.reduce((acc, assignment) => {
      // Try different possible field names for subject
      const subject =
        assignment.subject ||
        assignment.subject_name ||
        assignment.subjectName ||
        assignment.Subject ||
        'Unknown Subject';

      console.log('Processing assignment:', assignment, 'Subject:', subject);

      if (!acc[subject]) {
        acc[subject] = [];
      }
      acc[subject].push(assignment);
      return acc;
    }, {});

    console.log('Grouped assignments:', groups);

    // Convert to array and add counts
    const result = Object.keys(groups).map((subject) => ({
      subject,
      assignments: groups[subject],
      totalCount: groups[subject].length,
      incompleteCount: groups[subject].filter(
        (a) => !a.completed && !a.is_completed
      ).length,
      completedCount: groups[subject].filter(
        (a) => a.completed || a.is_completed
      ).length,
    }));

    console.log('Final subject groups:', result);
    return result;
  };

  // Helper function to get filtered and sorted assignments for a subject
  const getFilteredAssignments = (subjectAssignments) => {
    let filtered = subjectAssignments;

    // Filter by completion status - since API doesn't provide completion status,
    // we'll assume all assignments are incomplete for now
    if (!showCompleted) {
      // For now, show all assignments since we don't have completion status
      // In the future, this could be based on a completion field from API
      filtered = filtered.filter(
        (assignment) => !assignment.completed && !assignment.is_completed
      );
    }

    // Sort by deadline (most recent first)
    filtered.sort((a, b) => {
      const dateA = new Date(a.deadline || 0);
      const dateB = new Date(b.deadline || 0);
      return dateB - dateA;
    });

    return filtered;
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Helper function to get assignment status with modern icons
  const getAssignmentStatus = (assignment) => {
    // Since API doesn't provide completion status, we'll determine based on deadline
    const deadline = new Date(assignment.deadline || 0);
    const today = new Date();

    if (assignment.completed || assignment.is_completed) {
      return {
        status: 'completed',
        color: '#34C759',
        icon: faCheckCircle,
        label: 'Completed',
      };
    } else if (deadline < today) {
      return {
        status: 'overdue',
        color: '#FF3B30',
        icon: faExclamationTriangle,
        label: 'Overdue',
      };
    } else if (deadline.toDateString() === today.toDateString()) {
      return {
        status: 'due_today',
        color: '#FF9500',
        icon: faCalendarAlt,
        label: 'Due Today',
      };
    } else {
      return {
        status: 'pending',
        color: '#007AFF',
        icon: faClock,
        label: 'Pending',
      };
    }
  };

  const fetchAssignmentsData = async () => {
    if (!authCode) {
      Alert.alert('Error', 'Authentication code is missing');
      return;
    }

    setLoading(true);
    try {
      console.log('Fetching assignments with authCode:', authCode);
      const url = buildApiUrl(Config.API_ENDPOINTS.GET_STUDENT_HOMEWORK, {
        authCode,
      });
      console.log('Request URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('Raw assignments data:', JSON.stringify(data, null, 2));

        // Log the structure of the data
        console.log('Data type:', typeof data);
        console.log('Data keys:', Object.keys(data));

        if (Array.isArray(data)) {
          console.log('Data is an array with length:', data.length);
          if (data.length > 0) {
            console.log('First item structure:', Object.keys(data[0]));
            console.log('First item:', JSON.stringify(data[0], null, 2));
          }
        } else if (data && typeof data === 'object') {
          console.log('Data is an object');
          if (data.data && Array.isArray(data.data)) {
            console.log('data.data is an array with length:', data.data.length);
            if (data.data.length > 0) {
              console.log(
                'First data item structure:',
                Object.keys(data.data[0])
              );
              console.log(
                'First data item:',
                JSON.stringify(data.data[0], null, 2)
              );
            }
          }
        }

        setAssignments(data);
      } else {
        console.error(
          'Failed to fetch assignments:',
          response.status,
          response.statusText
        );
        const errorText = await response.text();
        console.error('Error response body:', errorText);
        Alert.alert('Error', `Failed to fetch assignments: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
      Alert.alert('Error', 'Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  // Render subject cards view
  const renderSubjectsView = () => {
    const subjectGroups = getSubjectGroups();

    if (subjectGroups.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <FontAwesomeIcon icon={faClipboardList} size={48} color='#8E8E93' />
          <Text style={styles.emptyText}>No assignments found</Text>
          <Text style={styles.emptySubtext}>
            Assignments will appear here once available
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.subjectsContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.subjectsGrid}>
          {subjectGroups.map((group) => {
            const subjectColor = getSubjectColor(group.subject);
            const subjectIcon = getSubjectIcon(group.subject);

            return (
              <TouchableOpacity
                key={group.subject}
                style={[
                  styles.modernSubjectCard,
                  { borderLeftColor: subjectColor },
                ]}
                onPress={() => setSelectedSubject(group)}
              >
                <View style={styles.subjectCardHeader}>
                  <View
                    style={[
                      styles.subjectIconContainer,
                      { backgroundColor: `${subjectColor}15` },
                    ]}
                  >
                    <FontAwesomeIcon
                      icon={subjectIcon}
                      size={24}
                      color={subjectColor}
                    />
                  </View>
                  <View style={styles.subjectInfo}>
                    <Text style={styles.modernSubjectTitle}>
                      {group.subject}
                    </Text>
                    <Text style={styles.subjectAssignmentCount}>
                      {group.totalCount} assignments
                    </Text>
                  </View>
                  <FontAwesomeIcon
                    icon={faChevronRight}
                    size={16}
                    color='#999'
                  />
                </View>

                <View style={styles.modernSubjectStats}>
                  <View style={styles.modernStatItem}>
                    <View
                      style={[styles.statBadge, { backgroundColor: '#FF3B30' }]}
                    >
                      <Text style={styles.statBadgeText}>
                        {group.incompleteCount}
                      </Text>
                    </View>
                    <Text style={styles.modernStatLabel}>Pending</Text>
                  </View>

                  <View style={styles.modernStatItem}>
                    <View
                      style={[styles.statBadge, { backgroundColor: '#34C759' }]}
                    >
                      <Text style={styles.statBadgeText}>
                        {group.completedCount}
                      </Text>
                    </View>
                    <Text style={styles.modernStatLabel}>Completed</Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>
    );
  };

  // Render assignments list for selected subject
  const renderAssignmentsView = () => {
    const filteredAssignments = getFilteredAssignments(
      selectedSubject.assignments
    );

    return (
      <View style={styles.assignmentsContainer}>
        {/* Header with back button and filter */}
        <View style={styles.assignmentsHeader}>
          <TouchableOpacity
            style={styles.backToSubjectsButton}
            onPress={() => setSelectedSubject(null)}
          >
            <FontAwesomeIcon icon={faArrowLeft} size={16} color='#007AFF' />
            <Text style={styles.backToSubjectsText}>Back to Subjects</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              showCompleted && styles.filterButtonActive,
            ]}
            onPress={() => setShowCompleted(!showCompleted)}
          >
            <Text
              style={[
                styles.filterButtonText,
                showCompleted && styles.filterButtonTextActive,
              ]}
            >
              {showCompleted ? 'Show All' : 'Show Completed'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Subject title */}
        <Text style={styles.selectedSubjectTitle}>
          {selectedSubject.subject}
        </Text>

        {/* Assignments list */}
        {filteredAssignments.length === 0 ? (
          <View style={styles.emptyAssignmentsContainer}>
            <Text style={styles.emptyAssignmentsText}>
              {showCompleted
                ? 'No completed assignments'
                : 'No pending assignments'}
            </Text>
          </View>
        ) : (
          <ScrollView
            style={styles.assignmentsList}
            showsVerticalScrollIndicator={false}
          >
            {filteredAssignments.map((assignment, index) => {
              const status = getAssignmentStatus(assignment);
              return (
                <View
                  key={assignment.uuid || assignment.id || index}
                  style={styles.modernAssignmentCard}
                >
                  <View style={styles.assignmentCardHeader}>
                    <View style={styles.assignmentCardLeft}>
                      <Text style={styles.modernAssignmentTitle}>
                        {assignment.title || 'Untitled Assignment'}
                      </Text>
                      <Text style={styles.assignmentDate}>
                        Due: {formatDate(assignment.deadline)}
                      </Text>
                    </View>
                    <View style={styles.assignmentCardRight}>
                      <View
                        style={[
                          styles.modernStatusBadge,
                          { backgroundColor: status.color },
                        ]}
                      >
                        <FontAwesomeIcon
                          icon={status.icon}
                          size={16}
                          color='#fff'
                        />
                      </View>
                    </View>
                  </View>

                  {assignment.homework_data && (
                    <Text
                      style={styles.assignmentDescription}
                      numberOfLines={3}
                    >
                      {assignment.homework_data.replace(/<[^>]*>/g, '')}
                    </Text>
                  )}

                  <View style={styles.assignmentCardBody}>
                    <View style={styles.assignmentDetails}>
                      {assignment.teacher_name && (
                        <View style={styles.assignmentDetailItem}>
                          <FontAwesomeIcon
                            icon={faUser}
                            size={14}
                            color='#666'
                          />
                          <Text style={styles.assignmentDetailText}>
                            {assignment.teacher_name}
                          </Text>
                        </View>
                      )}
                    </View>

                    <View style={styles.assignmentStatusContainer}>
                      <View
                        style={[
                          styles.assignmentStatusBadge,
                          { backgroundColor: status.color },
                        ]}
                      >
                        <Text style={styles.assignmentStatusText}>
                          {status.label}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
          </ScrollView>
        )}
      </View>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color='#007AFF' />
          <Text style={styles.loadingText}>Loading assignments data...</Text>
        </View>
      );
    }

    // Show assignments view if a subject is selected, otherwise show subjects
    if (selectedSubject) {
      return renderAssignmentsView();
    } else {
      return renderSubjectsView();
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={18} color='#fff' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Assignments</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Student Name Section - Hidden in landscape mode */}
      {!isLandscape && (
        <View style={styles.studentSection}>
          <Text style={styles.studentName}>{studentName || 'Student'}</Text>
          <Text style={styles.sectionSubtitle}>Assignments & Homework</Text>
        </View>
      )}

      <View style={styles.content}>
        {isLandscape ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.scrollContainer}
          >
            {renderContent()}
          </ScrollView>
        ) : (
          <View style={styles.scrollContainer}>{renderContent()}</View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 36,
  },
  studentSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  studentName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Subjects view styles
  subjectsContainer: {
    flex: 1,
  },
  subjectsGrid: {
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 10,
  },

  // Modern Subject Card Styles (similar to GradesScreen)
  modernSubjectCard: {
    backgroundColor: '#fff',
    width: '100%',
    marginVertical: 8,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
  },
  subjectCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  subjectIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  subjectInfo: {
    flex: 1,
  },
  modernSubjectTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subjectAssignmentCount: {
    fontSize: 14,
    color: '#666',
  },

  // Modern Stats Styles
  modernSubjectStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  modernStatItem: {
    alignItems: 'center',
  },
  statBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statBadgeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  modernStatLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  // Assignments view styles
  assignmentsContainer: {
    flex: 1,
  },
  assignmentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  backToSubjectsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  backToSubjectsText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
  },
  filterButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  selectedSubjectTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyAssignmentsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyAssignmentsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  assignmentsList: {
    flex: 1,
  },

  // Modern Assignment Card Styles
  modernAssignmentCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  assignmentCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  assignmentCardLeft: {
    flex: 1,
    marginRight: 15,
  },
  modernAssignmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  assignmentDate: {
    fontSize: 14,
    color: '#666',
  },
  assignmentCardRight: {
    alignItems: 'flex-end',
  },
  modernStatusBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  assignmentDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  assignmentCardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assignmentDetails: {
    flex: 1,
  },
  assignmentDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  assignmentDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  assignmentStatusContainer: {
    alignItems: 'flex-end',
  },
  assignmentStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  assignmentStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
});
