{"name": "edu-sis", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-navigation/elements": "^2.3.1", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "expo": "~52.0.41", "expo-build-properties": "^0.13.2", "expo-dev-client": "~5.0.20", "expo-modules-core": "^2.2.3", "expo-screen-orientation": "~8.0.4", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.77.2", "react-native-dotenv": "^3.4.11", "react-native-get-random-values": "^1.11.0", "react-native-orientation-locker": "^1.7.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.11.2", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}