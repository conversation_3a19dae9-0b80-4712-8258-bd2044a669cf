# Environment Configuration

This project uses environment variables to manage configuration across different environments (development, staging, production).

## Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Update the values in `.env` to match your environment:**
   - Replace `https://your-domain.com/mobile-api` with your actual API base URL
   - Update other configuration values as needed

## Environment Variables

### API Configuration
- `API_BASE_URL`: Base URL for all API calls
- `API_DOMAIN`: Domain name for the API server
- `API_*`: Individual API endpoint paths

### Web Resources
- `WEB_*`: Paths for web-based resources (calendar, contacts, etc.)

### App Configuration
- `APP_NAME`: Application display name
- `APP_VERSION`: Current version number
- `APP_BUNDLE_ID`: Bundle identifier for the app

### Development Configuration
- `USE_DUMMY_DATA`: Set to `true` to use mock data instead of real API calls
- `ENABLE_LOGGING`: Enable/disable console logging

### Network Configuration
- `NETWORK_TIMEOUT`: Request timeout in milliseconds
- `ENABLE_CLEARTEXT_TRAFFIC`: Allow HTTP traffic (for development)

### Device Configuration
- `DEFAULT_DEVICE_TYPE`: Default device type for API calls

## Usage in Code

Environment variables are accessed through the centralized config:

```javascript
import { Config, buildApiUrl } from '../config/env';

// Use configuration values
const apiUrl = buildApiUrl(Config.API_ENDPOINTS.GET_STUDENT_TIMETABLE, {
  authCode: 'your-auth-code'
});

// Access app configuration
const appName = Config.APP.NAME;
```

## Security Notes

- Never commit the `.env` file to version control
- Use `.env.example` to document required environment variables
- Keep sensitive values (API keys, secrets) in secure environment variable stores
- Use different `.env` files for different environments

## Deployment

For production deployments:
1. Set environment variables in your deployment platform
2. Ensure all required variables are configured
3. Test with production values before deploying
